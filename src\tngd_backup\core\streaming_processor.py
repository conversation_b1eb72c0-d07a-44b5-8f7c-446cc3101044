#!/usr/bin/env python3
"""
Streaming Data Processor Module

This module provides streaming data processing capabilities for handling large datasets
efficiently. It implements chunked data retrieval, processing, and storage while
maintaining memory efficiency and providing progress tracking.

Features:
- Chunked data processing with configurable chunk sizes
- Memory-efficient streaming without loading entire datasets
- Progress tracking and estimation
- Integration with existing retry and error handling
- Automatic fallback for small datasets
- Resource monitoring and adaptive throttling
"""

import os
import json
import time
import logging
import tempfile
import gc
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

# Import improved thread management
try:
    from .thread_manager import get_thread_manager, managed_thread_pool, log_thread_metrics
except ImportError:
    # Fallback if thread manager not available
    from contextlib import contextmanager

    @contextmanager
    def managed_thread_pool(name, max_workers=None):
        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=max_workers or 2) as pool:
            yield pool

    def log_thread_metrics():
        pass

# Configure logging
logger = logging.getLogger(__name__)


class ProcessingStrategy(Enum):
    """Data processing strategies."""
    STANDARD = "standard"  # Load all data at once (existing behavior)
    STREAMING = "streaming"  # Process data in chunks
    ADAPTIVE = "adaptive"  # Choose strategy based on data size


@dataclass
class ChunkInfo:
    """Information about a data chunk."""
    chunk_id: int
    offset: int
    limit: int
    estimated_rows: Optional[int] = None
    actual_rows: Optional[int] = None
    processing_time: Optional[float] = None
    memory_usage_mb: Optional[float] = None


@dataclass
class StreamingConfig:
    """Configuration for streaming data processing."""
    # Chunk size settings
    default_chunk_size: int = 100000  # 100K rows per chunk
    max_chunk_size: int = 500000  # 500K rows maximum
    min_chunk_size: int = 10000   # 10K rows minimum
    
    # Thresholds for streaming activation
    streaming_threshold_rows: int = 1000000  # 1M rows triggers streaming
    memory_threshold_mb: int = 1000  # 1GB memory threshold
    
    # Progress and monitoring
    progress_report_interval: int = 5  # Report progress every 5 chunks
    memory_check_interval: int = 3     # Check memory every 3 chunks
    
    # Adaptive settings
    enable_adaptive_chunking: bool = True
    chunk_size_adjustment_factor: float = 0.8  # Reduce chunk size by 20% if memory pressure
    
    # File handling
    temp_file_prefix: str = "streaming_chunk_"
    cleanup_temp_files: bool = True


class ChunkManager:
    """Manages chunk calculation and pagination logic."""
    
    def __init__(self, config: StreamingConfig):
        """Initialize the chunk manager."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.ChunkManager")
    
    def calculate_chunks(self, total_rows: int, table_name: Optional[str] = None) -> List[ChunkInfo]:
        """
        Calculate optimal chunks for a dataset.
        
        Args:
            total_rows: Total number of rows to process
            table_name: Optional table name for logging
            
        Returns:
            List of ChunkInfo objects
        """
        if total_rows <= 0:
            return []
        
        # Determine chunk size
        chunk_size = self._calculate_optimal_chunk_size(total_rows, table_name)
        
        # Calculate number of chunks
        num_chunks = (total_rows + chunk_size - 1) // chunk_size
        
        chunks = []
        for i in range(num_chunks):
            offset = i * chunk_size
            limit = min(chunk_size, total_rows - offset)
            
            chunk = ChunkInfo(
                chunk_id=i + 1,
                offset=offset,
                limit=limit,
                estimated_rows=limit
            )
            chunks.append(chunk)
        
        self.logger.info(f"Calculated {num_chunks} chunks for {total_rows:,} rows "
                        f"(chunk size: {chunk_size:,})")
        
        return chunks
    
    def _calculate_optimal_chunk_size(self, total_rows: int, table_name: Optional[str] = None) -> int:
        """Calculate optimal chunk size based on dataset characteristics."""
        # Start with default chunk size
        chunk_size = self.config.default_chunk_size
        
        # Adjust based on total dataset size
        if total_rows > 10_000_000:  # 10M+ rows
            chunk_size = min(self.config.max_chunk_size, chunk_size * 2)
        elif total_rows < 100_000:   # < 100K rows
            chunk_size = max(self.config.min_chunk_size, chunk_size // 2)
        
        # Ensure chunk size is within bounds
        chunk_size = max(self.config.min_chunk_size, 
                        min(self.config.max_chunk_size, chunk_size))
        
        self.logger.debug(f"Calculated chunk size: {chunk_size:,} for {total_rows:,} rows")
        return chunk_size
    
    def adjust_chunk_size(self, current_chunk_size: int, memory_pressure: bool) -> int:
        """Adjust chunk size based on memory pressure."""
        if not self.config.enable_adaptive_chunking:
            return current_chunk_size
        
        if memory_pressure:
            new_size = int(current_chunk_size * self.config.chunk_size_adjustment_factor)
            new_size = max(self.config.min_chunk_size, new_size)
            self.logger.info(f"Reducing chunk size due to memory pressure: "
                           f"{current_chunk_size:,} -> {new_size:,}")
            return new_size
        
        return current_chunk_size


class ProgressTracker:
    """Tracks progress across chunks with existing logging patterns."""
    
    def __init__(self, total_chunks: int, total_rows: int, log_callback: Callable[[str, str, str], None]):
        """
        Initialize progress tracker.
        
        Args:
            total_chunks: Total number of chunks to process
            total_rows: Total number of rows to process
            log_callback: Logging callback function (log_step method)
        """
        self.total_chunks = total_chunks
        self.total_rows = total_rows
        self.log_step = log_callback
        
        self.processed_chunks = 0
        self.processed_rows = 0
        self.start_time = time.time()
        self.chunk_times = []
        
        self.logger = logging.getLogger(f"{__name__}.ProgressTracker")
    
    def update_progress(self, chunk: ChunkInfo):
        """Update progress after processing a chunk."""
        self.processed_chunks += 1
        if chunk.actual_rows:
            self.processed_rows += chunk.actual_rows
        
        if chunk.processing_time:
            self.chunk_times.append(chunk.processing_time)
        
        # Log progress
        self._log_progress()
    
    def _log_progress(self):
        """Log current progress using established logging patterns."""
        elapsed_time = time.time() - self.start_time
        
        # Calculate progress percentages
        chunk_progress = (self.processed_chunks / self.total_chunks) * 100
        row_progress = (self.processed_rows / self.total_rows) * 100 if self.total_rows > 0 else 0
        
        # Estimate remaining time
        remaining_time_str = self._estimate_remaining_time()
        
        # Log using established pattern
        self.log_step("STREAMING_PROGRESS",
                     f"Chunk {self.processed_chunks}/{self.total_chunks} "
                     f"({chunk_progress:.1f}%) | "
                     f"Rows: {self.processed_rows:,}/{self.total_rows:,} "
                     f"({row_progress:.1f}%) | "
                     f"Elapsed: {elapsed_time/BackupConstants.SECONDS_PER_MINUTE:.1f}m | "
                     f"Remaining: {remaining_time_str}",
                     "INFO")
    
    def _estimate_remaining_time(self) -> str:
        """Estimate remaining processing time."""
        if self.processed_chunks == 0 or not self.chunk_times:
            return "calculating..."
        
        avg_chunk_time = sum(self.chunk_times) / len(self.chunk_times)
        remaining_chunks = self.total_chunks - self.processed_chunks
        remaining_seconds = remaining_chunks * avg_chunk_time
        
        if remaining_seconds < BackupConstants.SECONDS_PER_MINUTE:
            return f"{remaining_seconds:.0f}s"
        elif remaining_seconds < BackupConstants.SECONDS_PER_HOUR:
            return f"{remaining_seconds/BackupConstants.SECONDS_PER_MINUTE:.1f}m"
        else:
            return f"{remaining_seconds/BackupConstants.SECONDS_PER_HOUR:.1f}h"
    
    def get_summary(self) -> Dict[str, Any]:
        """Get processing summary."""
        total_time = time.time() - self.start_time
        avg_chunk_time = sum(self.chunk_times) / len(self.chunk_times) if self.chunk_times else 0
        
        return {
            'total_chunks': self.total_chunks,
            'processed_chunks': self.processed_chunks,
            'total_rows': self.total_rows,
            'processed_rows': self.processed_rows,
            'total_time_seconds': total_time,
            'average_chunk_time_seconds': avg_chunk_time,
            'rows_per_second': self.processed_rows / total_time if total_time > 0 else 0
        }


class StreamingFileWriter:
    """Handles incremental file writing to avoid memory issues."""

    def __init__(self, output_path: str, config: StreamingConfig):
        """
        Initialize streaming file writer.

        Args:
            output_path: Path to the output file
            config: Streaming configuration
        """
        self.output_path = output_path
        self.config = config
        self.temp_files = []
        self.logger = logging.getLogger(f"{__name__}.StreamingFileWriter")

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

    def write_chunk(self, chunk_data: List[Dict[str, Any]], chunk: ChunkInfo) -> str:
        """
        Write a chunk of data to a temporary file.

        Args:
            chunk_data: Data to write
            chunk: Chunk information

        Returns:
            Path to the temporary file
        """
        # Create temporary file for this chunk
        temp_fd, temp_path = tempfile.mkstemp(
            suffix='.json',
            prefix=f"{self.config.temp_file_prefix}chunk_{chunk.chunk_id:04d}_",
            dir=os.path.dirname(self.output_path)
        )

        try:
            with os.fdopen(temp_fd, 'w', encoding='utf-8') as f:
                json.dump(chunk_data, f, ensure_ascii=False, separators=(',', ':'))

            self.temp_files.append(temp_path)

            # Update chunk info
            chunk.actual_rows = len(chunk_data)
            file_size_mb = os.path.getsize(temp_path) / BackupConstants.BYTES_PER_MB

            self.logger.debug(f"Wrote chunk {chunk.chunk_id} to {temp_path} "
                            f"({chunk.actual_rows:,} rows, {file_size_mb:.1f}MB)")

            return temp_path

        except Exception as e:
            # Clean up file descriptor if still open
            try:
                os.close(temp_fd)
            except (OSError, ValueError):
                # OSError: if file descriptor is already closed or invalid
                # ValueError: if file descriptor is negative or not an integer
                pass
            # Remove temp file if created
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise e

    def merge_chunks(self) -> str:
        """
        Merge all chunk files into the final output file.

        Returns:
            Path to the final merged file
        """
        if not self.temp_files:
            raise ValueError("No chunk files to merge")

        self.logger.info(f"Merging {len(self.temp_files)} chunk files into {self.output_path}")

        start_time = time.time()
        total_rows = 0

        with open(self.output_path, 'w', encoding='utf-8') as output_file:
            output_file.write('[')

            for i, temp_file in enumerate(self.temp_files):
                if i > 0:
                    output_file.write(',')

                # Read and write chunk data
                with open(temp_file, 'r', encoding='utf-8') as chunk_file:
                    chunk_data = json.load(chunk_file)
                    total_rows += len(chunk_data)

                    # Write chunk data (excluding outer brackets)
                    for j, record in enumerate(chunk_data):
                        if j > 0:
                            output_file.write(',')
                        json.dump(record, output_file, ensure_ascii=False, separators=(',', ':'))

            output_file.write(']')

        merge_time = time.time() - start_time
        file_size_mb = os.path.getsize(self.output_path) / BackupConstants.BYTES_PER_MB

        self.logger.info(f"Merged {total_rows:,} rows into {self.output_path} "
                        f"({file_size_mb:.1f}MB) in {merge_time:.1f}s")

        return self.output_path

    def cleanup_temp_files(self):
        """Clean up temporary chunk files."""
        if not self.config.cleanup_temp_files:
            return

        cleaned_count = 0
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    cleaned_count += 1
            except Exception as e:
                self.logger.warning(f"Failed to clean up temp file {temp_file}: {e}")

        if cleaned_count > 0:
            self.logger.debug(f"Cleaned up {cleaned_count} temporary files")

        self.temp_files.clear()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):  # type: ignore
        """Context manager exit with cleanup."""
        self.cleanup_temp_files()


class StreamingDataProcessor:
    """Main orchestrator for chunked data processing."""

    def __init__(self, config: StreamingConfig, log_callback: Callable[[str, str, str], None]):
        """
        Initialize streaming data processor.

        Args:
            config: Streaming configuration
            log_callback: Logging callback function (log_step method)
        """
        self.config = config
        self.log_step = log_callback
        self.logger = logging.getLogger(f"{__name__}.StreamingDataProcessor")

        # Initialize components
        self.chunk_manager = ChunkManager(config)

        # Resource monitoring (if available)
        self.resource_monitor = None
        try:
            from .resource_monitor import ResourceMonitor
            self.resource_monitor = ResourceMonitor()
        except ImportError:
            self.logger.debug("Resource monitor not available")

    def should_use_streaming(self, estimated_rows: int, table_name: Optional[str] = None) -> bool:
        """
        Determine if streaming should be used for a dataset.

        Args:
            estimated_rows: Estimated number of rows
            table_name: Optional table name for logging

        Returns:
            True if streaming should be used
        """
        # If estimated rows is 0, don't use streaming
        if estimated_rows == 0:
            self.log_step("STREAMING_DECISION",
                         f"Using standard processing for {table_name or 'table'}: "
                         f"No rows estimated",
                         "INFO")
            return False

        # Check row count threshold
        if estimated_rows >= self.config.streaming_threshold_rows:
            self.log_step("STREAMING_DECISION",
                         f"Using streaming for {table_name or 'table'}: "
                         f"{estimated_rows:,} rows >= {self.config.streaming_threshold_rows:,} threshold",
                         "INFO")
            return True

        # Check memory threshold if resource monitor is available
        # But only if we have a reasonable row estimate
        if self.resource_monitor and estimated_rows > 0:
            try:
                metrics = self.resource_monitor.get_current_metrics()
                memory_mb = metrics.memory_mb

                # Only use memory-based streaming if we have significant data
                if memory_mb >= self.config.memory_threshold_mb and estimated_rows >= 100:
                    self.log_step("STREAMING_DECISION",
                                 f"Using streaming for {table_name or 'table'}: "
                                 f"Memory usage {memory_mb:.0f}MB >= {self.config.memory_threshold_mb}MB threshold "
                                 f"(estimated {estimated_rows:,} rows)",
                                 "INFO")
                    return True
            except Exception as e:
                self.logger.debug(f"Failed to check memory usage: {e}")

        self.log_step("STREAMING_DECISION",
                     f"Using standard processing for {table_name or 'table'}: "
                     f"{estimated_rows:,} rows < {self.config.streaming_threshold_rows:,} threshold",
                     "INFO")
        return False

    def process_data_streaming(self,
                              query_func: Callable[[int, int], List[Dict[str, Any]]],
                              total_rows: int,
                              output_path: str,
                              table_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Process data using streaming approach.

        Args:
            query_func: Function to query data chunks (offset, limit) -> data
            total_rows: Total number of rows to process
            output_path: Path to save the final output file
            table_name: Optional table name for logging

        Returns:
            Processing results dictionary
        """
        start_time = time.time()

        # Calculate chunks
        chunks = self.chunk_manager.calculate_chunks(total_rows, table_name)

        if not chunks:
            return {
                'status': 'no_data',
                'total_rows': 0,
                'chunks_processed': 0,
                'processing_time': 0,
                'output_file': None
            }

        # Initialize progress tracker
        progress_tracker = ProgressTracker(len(chunks), total_rows, self.log_step)

        # Initialize file writer
        with StreamingFileWriter(output_path, self.config) as file_writer:
            processed_rows = 0
            failed_chunks = []

            self.log_step("STREAMING_START",
                         f"Starting streaming processing: {len(chunks)} chunks, "
                         f"{total_rows:,} total rows",
                         "INFO")

            for chunk in chunks:
                chunk_start_time = time.time()

                try:
                    # Check memory pressure and adjust if needed
                    if self.resource_monitor and chunk.chunk_id % self.config.memory_check_interval == 0:
                        try:
                            metrics = self.resource_monitor.get_current_metrics()
                            memory_pressure = metrics.memory_percent > 80  # 80% threshold

                            if memory_pressure:
                                self.log_step("MEMORY_PRESSURE",
                                             f"High memory usage detected: {metrics.memory_percent:.1f}%",
                                             "WARNING")
                                # Apply throttling if available
                                if hasattr(self.resource_monitor, 'apply_throttling'):
                                    self.resource_monitor.apply_throttling(f"chunk_{chunk.chunk_id}")
                        except Exception as e:
                            self.logger.debug(f"Memory check failed: {e}")

                    # Query chunk data
                    chunk_data = query_func(chunk.offset, chunk.limit)

                    if not chunk_data:
                        self.logger.warning(f"No data returned for chunk {chunk.chunk_id}")
                        chunk.actual_rows = 0
                    else:
                        # Write chunk to temporary file
                        file_writer.write_chunk(chunk_data, chunk)
                        processed_rows += chunk.actual_rows or 0

                    # Update timing
                    chunk.processing_time = time.time() - chunk_start_time

                    # Update progress
                    progress_tracker.update_progress(chunk)

                except Exception as e:
                    chunk.processing_time = time.time() - chunk_start_time
                    failed_chunks.append({
                        'chunk_id': chunk.chunk_id,
                        'offset': chunk.offset,
                        'limit': chunk.limit,
                        'error': str(e)
                    })

                    self.log_step("CHUNK_ERROR",
                                 f"Failed to process chunk {chunk.chunk_id}: {str(e)}",
                                 "ERROR")

            # Merge all chunks into final file
            if file_writer.temp_files:
                self.log_step("STREAMING_MERGE", "Merging chunk files...", "INFO")
                final_file = file_writer.merge_chunks()
            else:
                final_file = None

        # Calculate final results
        total_time = time.time() - start_time
        processing_summary = progress_tracker.get_summary()

        result = {
            'status': 'completed' if not failed_chunks else 'partial_failure',
            'total_rows': processed_rows,
            'chunks_processed': len(chunks) - len(failed_chunks),
            'chunks_failed': len(failed_chunks),
            'failed_chunks': failed_chunks,
            'processing_time': total_time,
            'output_file': final_file,
            'processing_summary': processing_summary
        }

        # Log final results
        if failed_chunks:
            self.log_step("STREAMING_COMPLETE",
                         f"Streaming completed with {len(failed_chunks)} failed chunks. "
                         f"Processed {processed_rows:,} rows in {total_time:.1f}s",
                         "WARNING")
        else:
            self.log_step("STREAMING_COMPLETE",
                         f"Streaming completed successfully. "
                         f"Processed {processed_rows:,} rows in {total_time:.1f}s",
                         "INFO")

        return result
